{"openapi": "3.1.0", "info": {"title": "NinjaAPI", "version": "1.0.0", "description": ""}, "paths": {"/api/docs/tag/{tag}": {"get": {"operationId": "core_api_docs_get_docs_by_tag", "summary": "Get API documentation for a specific tag", "parameters": [{"in": "path", "name": "tag", "schema": {"title": "Tag", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK"}}, "description": "Get OpenAPI documentation for all endpoints with a specific tag,\nincluding the request/response schemas they use.", "tags": ["docs"]}}, "/api/accounts/": {"post": {"operationId": "accounts_api_login", "summary": "<PERSON><PERSON>", "parameters": [{"in": "query", "name": "phone", "schema": {"title": "Phone", "type": "string"}, "required": true}, {"in": "query", "name": "password", "schema": {"title": "Password", "type": "string"}, "required": true}, {"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginOutSchema"}}}}}, "description": "Login to the system and retrun token and user info", "tags": ["accounts"]}}, "/api/accounts/me": {"get": {"operationId": "accounts_api_get_me", "summary": "Get Me", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "description": "Get the current user", "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/create": {"post": {"operationId": "accounts_api_create_user", "summary": "Create User", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/edit/{user_id}": {"put": {"operationId": "accounts_api_edit_user", "summary": "Edit User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/delete/{user_id}": {"delete": {"operationId": "accounts_api_delete_user", "summary": "Delete User", "parameters": [{"in": "path", "name": "user_id", "schema": {"title": "User Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}, "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/update-location": {"post": {"operationId": "accounts_api_update_location", "summary": "Update Location", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserModelSchema"}}}}}, "description": "Update the current user's location coordinates", "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LocationUpdateSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/employees/{employee_id}/stats/": {"get": {"operationId": "accounts_api_get_employee_stats", "summary": "Get Employee Stats", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}, {"in": "query", "name": "date_from", "schema": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Date From"}, "required": false}, {"in": "query", "name": "date_to", "schema": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Date To"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeStatsResponseSchema"}}}}}, "description": "Get statistics for an employee within a specific date range", "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/employees/{employee_id}/balance-transactions/": {"get": {"operationId": "accounts_api_get_employee_balance_transactions", "summary": "Get Employee Balance Transactions", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/EmployeeBalanceTransactionSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/employees/{employee_id}/settle/": {"post": {"operationId": "accounts_api_settle_employee_balance", "summary": "Settle Employee Balance", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeeBalanceTransactionSchema"}}}}}, "tags": ["accounts"], "security": [{"AuthBearer": []}]}}, "/api/accounts/qr/generate": {"post": {"operationId": "accounts_api_generate_qr_code", "summary": "Generate Qr Code", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/QRCodeGenerateResponseSchema"}}}}}, "description": "Generate a time-limited, single-use QR token that can be used for password-less login.\n\nOnly users with the `MASTER` role are allowed to generate QR codes. The generated\ntoken is stored in Redis (via Django cache) with a short TTL to prevent reuse.", "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QRCodeGenerateSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/accounts/qr/login": {"post": {"operationId": "accounts_api_login_with_qr", "summary": "Login With Qr", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginOutSchema"}}}}}, "description": "Authenticate a user using a QR token.\n\nThe user provides only the QR token obtained by scanning the code generated for\nthem by a master user. The token is validated for:\n1. Existence and not being expired (cache lookup).\n2. Belonging to a valid employee.\n\nUpon successful validation the token is deleted to enforce single-use and the\nuser receives a regular JWT token identical to the standard login flow.", "tags": ["accounts"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/QRCodeLoginSchema"}}}, "required": true}}}, "/api/orders/{order_id}/": {"get": {"operationId": "orders_api_get_order", "summary": "Get Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchemaWithHistory"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}, "put": {"operationId": "orders_api_edit_order", "summary": "Edit Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_api_delete_order", "summary": "Delete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"s": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "u": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "c": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}, "e": {"description": "Unknown Status Code", "content": {"application/json": {"schema": {"title": "Response", "type": "boolean"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders": {"get": {"operationId": "orders_api_list_orders", "summary": "List Orders", "parameters": [{"in": "query", "name": "date_from", "schema": {"title": "Date From", "type": "string"}, "required": false}, {"in": "query", "name": "date_to", "schema": {"title": "Date To", "type": "string"}, "required": false}, {"in": "query", "name": "status", "schema": {"allOf": [{"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatus", "type": "string"}]}, "required": false}, {"in": "query", "name": "assigned_to", "schema": {"title": "Assigned To", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderExtendedOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders/": {"post": {"operationId": "orders_api_add_order", "summary": "Add Order", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/assign/": {"post": {"operationId": "orders_api_assign_order", "summary": "Assign Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderAssignSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/transfer/": {"post": {"operationId": "orders_api_transfer_order", "summary": "Transfer Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderTransferSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/proof/": {"post": {"operationId": "orders_api_add_proof", "summary": "Add Proof", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderProofOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"multipart/form-data": {"schema": {"title": "MultiPartBodyParams", "type": "object", "properties": {"proof_type": {"title": "Proof Type", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}, "img": {"format": "binary", "title": "Img", "type": "string"}}, "required": ["proof_type"]}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/complete/": {"post": {"operationId": "orders_api_complete_order", "summary": "Complete Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderCompleteSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders_statuses": {"get": {"operationId": "orders_api_list_order_statuses", "summary": "List Order Statuses", "parameters": [{"in": "query", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/OrderStatusOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["orders"], "security": [{"AuthBearer": []}]}}, "/api/orders/{order_id}/accept/": {"post": {"operationId": "orders_api_accept_order", "summary": "Accept Order", "parameters": [{"in": "path", "name": "order_id", "schema": {"title": "Order Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OrderOutSchema"}}}}}, "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"allOf": [{"$ref": "#/components/schemas/OrderProofInSchema"}]}}}, "required": false}, "security": [{"AuthBearer": []}]}}, "/api/orders/import-excel": {"post": {"operationId": "orders_api_import_orders_from_excel", "summary": "Import Orders From Excel", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Import orders from an Excel file.\n\nArgs:\n    excel_file: The Excel file to import\n    skip_duplicates: Whether to skip orders with duplicate codes\n    create_missing_companies: Whether to create companies that don't exist\n    default_company_id: Default company ID for orders without company\n    use_intelligent_mapping: Whether to use intelligent column detection (recommended)\n\nReturns:\n    Import results with statistics and any errors", "tags": ["orders"], "requestBody": {"content": {"multipart/form-data": {"schema": {"properties": {"excel_file": {"format": "binary", "title": "Excel File", "type": "string"}, "skip_duplicates": {"default": true, "title": "Skip Duplicates", "type": "boolean"}, "create_missing_companies": {"default": false, "title": "Create Missing Companies", "type": "boolean"}, "default_company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Default Company Id"}, "use_intelligent_mapping": {"default": true, "title": "Use Intelligent Mapping", "type": "boolean"}}, "required": ["excel_file"], "title": "MultiPartBodyParams", "type": "object"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/export/preview": {"post": {"operationId": "orders_api_preview_export", "summary": "Preview Export", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Preview orders that would be exported with the given filters.\n\nArgs:\n    payload: Bulk export request with filters\n\nReturns:\n    Preview information including count and sample orders", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkExportRequest"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/export/selective": {"post": {"operationId": "orders_api_export_orders_selective", "summary": "Export Orders Selective", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Export selected orders to CSV file.\n\nArgs:\n    payload: Selective export request with order IDs\n\nReturns:\n    CSV file download response", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/SelectiveExportRequest"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/export/bulk": {"post": {"operationId": "orders_api_export_orders_bulk", "summary": "Export Orders Bulk", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Export orders with filters to CSV file.\n\nArgs:\n    payload: Bulk export request with filters\n\nReturns:\n    CSV file download response", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkExportRequest"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/orders/export/bulk-arabic": {"post": {"operationId": "orders_api_export_orders_bulk_arabic", "summary": "Export Orders Bulk Arabic", "parameters": [], "responses": {"200": {"description": "OK"}}, "description": "Export orders with filters to CSV file with Arabic headers.\n\nArgs:\n    payload: Bulk export request with filters\n\nReturns:\n    CSV file download response with Arabic headers", "tags": ["orders"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkExportRequest"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/companies": {"get": {"operationId": "orders_api_list_companies", "summary": "List Companies", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CompanyOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["companies"], "security": [{"AuthBearer": []}]}}, "/api/companies/": {"post": {"operationId": "orders_api_add_company", "summary": "Add Company", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOutSchema"}}}}}, "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/companies/{company_id}/": {"put": {"operationId": "orders_api_edit_company", "summary": "Edit Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOutSchema"}}}}}, "tags": ["companies"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_api_delete_company", "summary": "Delete Company", "parameters": [{"in": "path", "name": "company_id", "schema": {"title": "Company Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}, "tags": ["companies"], "security": [{"AuthBearer": []}]}}, "/api/companies/code/": {"get": {"operationId": "orders_api_get_company_by_code", "summary": "Get Company By Code", "parameters": [{"in": "query", "name": "code", "schema": {"title": "Code", "type": "string"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyOutSchema"}}}}}, "tags": ["companies"], "security": [{"AuthBearer": []}]}}, "/api/company-channels": {"get": {"operationId": "orders_api_list_company_channels", "summary": "List Company Channels", "parameters": [{"in": "query", "name": "company_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Company Id"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/CompanyChannelOutSchema"}, "title": "Response", "type": "array"}}}}}, "tags": ["company-channels"], "security": [{"AuthBearer": []}]}}, "/api/company-channels/": {"post": {"operationId": "orders_api_add_company_channel", "summary": "Add Company Channel", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelOutSchema"}}}}}, "tags": ["company-channels"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelInSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/company-channels/{channel_id}/": {"get": {"operationId": "orders_api_get_company_channel", "summary": "Get Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelOutSchema"}}}}}, "tags": ["company-channels"], "security": [{"AuthBearer": []}]}, "put": {"operationId": "orders_api_edit_company_channel", "summary": "Edit Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelOutSchema"}}}}}, "tags": ["company-channels"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/CompanyChannelEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_api_delete_company_channel", "summary": "Delete Company Channel", "parameters": [{"in": "path", "name": "channel_id", "schema": {"title": "Channel Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SuccessResponse"}}}}}, "tags": ["company-channels"], "security": [{"AuthBearer": []}]}}, "/api/offices/{office_id}/": {"put": {"operationId": "offices_api_edit_office", "summary": "Edit Office", "parameters": [{"in": "path", "name": "office_id", "schema": {"title": "Office Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeOutSchema"}}}}}, "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficeEditSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/offices/users": {"get": {"operationId": "offices_api_list_office_users", "summary": "List Office Users", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"items": {"$ref": "#/components/schemas/UserModelSchema"}, "title": "Response", "type": "array"}}}}}, "description": "List all users who belong to the same office.", "tags": ["offices"], "security": [{"AuthBearer": []}]}}, "/api/offices/performance": {"post": {"operationId": "offices_api_get_office_performance", "summary": "Get Office Performance", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/OfficePerformanceSchema"}}}}}, "description": "Get performance report for the entire office.", "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateRangeSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/offices/employees/{employee_id}/performance/": {"post": {"operationId": "offices_api_get_employee_performance", "summary": "Get Employee Performance", "parameters": [{"in": "path", "name": "employee_id", "schema": {"title": "Employee Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/EmployeePerformanceSchema"}}}}}, "description": "Get performance report for a specific employee.", "tags": ["offices"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/DateRangeSchema"}}}, "required": true}, "security": [{"AuthBearer": []}]}}, "/api/offices/historical-activities": {"get": {"operationId": "offices_api_get_historical_activities", "summary": "Get Historical Activities", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HistoricalActivityResponseSchema"}}}}}, "description": "Get historical activity logs for the office (dashboard view).", "tags": ["offices"], "security": [{"AuthBearer": []}]}}, "/api/offices/historical-activities-v2": {"get": {"operationId": "offices_api_get_historical_activities_v2", "summary": "Get Historical Activities V2", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "title": "Page", "type": "integer"}, "required": false}, {"in": "query", "name": "page_size", "schema": {"default": 20, "title": "<PERSON>", "type": "integer"}, "required": false}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HistoricalActivityResponseSchema"}}}}}, "description": "Ultra-optimized version using database-level pagination and raw SQL for maximum performance.\n\nThis version is recommended for offices with very large activity histories (>10k records).", "tags": ["offices"], "security": [{"AuthBearer": []}]}}, "/api/orders-bulk/": {"get": {"operationId": "orders_bulk_import_api_list_bulk_imports", "summary": "List Bulk Imports", "parameters": [{"in": "query", "name": "page", "schema": {"default": 1, "description": "Page number", "minimum": 1, "title": "Page", "type": "integer"}, "required": false, "description": "Page number"}, {"in": "query", "name": "page_size", "schema": {"default": 20, "description": "Items per page", "maximum": 100, "minimum": 1, "title": "<PERSON>", "type": "integer"}, "required": false, "description": "Items per page"}, {"in": "query", "name": "search", "schema": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in name or filename", "title": "Search"}, "required": false, "description": "Search in name or filename"}, {"in": "query", "name": "imported_by_id", "schema": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by user who imported", "title": "Imported By Id"}, "required": false, "description": "Filter by user who imported"}, {"in": "query", "name": "date_from", "schema": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "Filter imports from this date", "title": "Date From"}, "required": false, "description": "Filter imports from this date"}, {"in": "query", "name": "date_to", "schema": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "Filter imports to this date", "title": "Date To"}, "required": false, "description": "Filter imports to this date"}, {"in": "query", "name": "min_success_rate", "schema": {"anyOf": [{"maximum": 100, "minimum": 0, "type": "number"}, {"type": "null"}], "description": "Minimum success rate", "title": "Min Success Rate"}, "required": false, "description": "Minimum success rate"}, {"in": "query", "name": "has_orders", "schema": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by whether bulk import has orders", "title": "Has Orders"}, "required": false, "description": "Filter by whether bulk import has orders"}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkImportListResponse"}}}}}, "description": "List all bulk import sessions for the user's office with filtering and pagination.\n\nArgs:\n    filters: Filtering and pagination parameters\n\nReturns:\n    Paginated list of bulk import sessions", "tags": ["orders-bulk"], "security": [{"AuthBearer": []}]}}, "/api/orders-bulk/{bulk_import_id}": {"get": {"operationId": "orders_bulk_import_api_get_bulk_import_detail", "summary": "Get Bulk Import Detail", "parameters": [{"in": "path", "name": "bulk_import_id", "schema": {"title": "Bulk Import Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkImportDetailResponse"}}}}}, "description": "Get detailed information about a specific bulk import session.\n\nArgs:\n    bulk_import_id: ID of the bulk import session\n\nReturns:\n    Detailed bulk import information", "tags": ["orders-bulk"], "security": [{"AuthBearer": []}]}, "delete": {"operationId": "orders_bulk_import_api_delete_bulk_import", "summary": "Delete Bulk Import", "parameters": [{"in": "path", "name": "bulk_import_id", "schema": {"title": "Bulk Import Id", "type": "integer"}, "required": true}], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkImportDeleteResponse"}}}}}, "description": "Delete a bulk import session and all its associated orders.\n\nArgs:\n    bulk_import_id: ID of the bulk import session to delete\n\nReturns:\n    Deletion confirmation with statistics", "tags": ["orders-bulk"], "security": [{"AuthBearer": []}]}}, "/api/orders-bulk/stats": {"get": {"operationId": "orders_bulk_import_api_get_bulk_import_stats", "summary": "Get Bulk Import Stats", "parameters": [], "responses": {"200": {"description": "OK", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BulkImportStatsResponse"}}}}}, "description": "Get statistics about bulk imports for the user's office.\n\nReturns:\n    Bulk import statistics", "tags": ["orders-bulk"], "security": [{"AuthBearer": []}]}}}, "components": {"schemas": {"LoginOutSchema": {"properties": {"token": {"title": "Token", "type": "string"}, "user": {"$ref": "#/components/schemas/UserModelSchema"}}, "required": ["token", "user"], "title": "LoginOutSchema", "type": "object"}, "Office": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["name", "created_at", "updated_at"], "title": "Office", "type": "object"}, "RoleEnum": {"enum": ["MASTER", "MANAGER", "EMPLOYEE", "CUSTOM_USER"], "title": "RoleEnum", "type": "string"}, "UserModelSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "username": {"description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "maxLength": 150, "title": "Username", "type": "string"}, "first_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "First Name"}, "last_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "Last Name"}, "email": {"anyOf": [{"format": "email", "type": "string"}, {"type": "null"}], "description": "", "title": "Email Address"}, "date_joined": {"description": "", "format": "date-time", "title": "Date Joined", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/RoleEnum"}], "default": "EMPLOYEE", "description": "", "title": "Role"}, "office": {"anyOf": [{"$ref": "#/components/schemas/Office"}, {"type": "null"}], "description": "", "title": "Office"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Commission rate percentage for delivery employees", "title": "Commission Rate"}, "current_location_lat": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lat"}, "current_location_lng": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lng"}, "last_location_update": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Location Update"}}, "required": ["username"], "title": "UserModelSchema", "type": "object"}, "Role": {"enum": ["MASTER", "MANAGER", "EMPLOYEE", "CUSTOM_USER"], "title": "Role", "type": "string"}, "UserCreateSchema": {"properties": {"username": {"title": "Username", "type": "string"}, "password": {"title": "Password", "type": "string"}, "first_name": {"title": "First Name", "type": "string"}, "last_name": {"title": "Last Name", "type": "string"}, "email": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Email"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/Role"}], "default": "EMPLOYEE"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Commission Rate"}}, "required": ["username", "password", "phone"], "title": "UserCreateSchema", "type": "object"}, "UserEditSchema": {"properties": {"username": {"minLength": 3, "title": "Username", "type": "string"}, "first_name": {"minLength": 3, "title": "First Name", "type": "string"}, "last_name": {"minLength": 3, "title": "Last Name", "type": "string"}, "email": {"format": "email", "title": "Email", "type": "string"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/Role"}]}, "commission_rate": {"maximum": 100, "minimum": 0, "title": "Commission Rate", "type": "number"}}, "title": "UserEditSchema", "type": "object"}, "SuccessResponse": {"properties": {"success": {"title": "Success", "type": "boolean"}}, "required": ["success"], "title": "SuccessResponse", "type": "object"}, "LocationUpdateSchema": {"properties": {"lat": {"title": "Lat", "type": "number"}, "lng": {"title": "Lng", "type": "number"}}, "required": ["lat", "lng"], "title": "LocationUpdateSchema", "type": "object"}, "EmployeeStatsResponseSchema": {"properties": {"total_money_deserved": {"title": "Total Money Deserved", "type": "number"}, "total_orders_completed": {"title": "Total Orders Completed", "type": "integer"}, "total_orders_assigned": {"title": "Total Orders Assigned", "type": "integer"}, "total_money_to_collect": {"title": "Total Money To Collect", "type": "number"}, "total_money_collected": {"title": "Total Money Collected", "type": "number"}}, "required": ["total_money_deserved", "total_orders_completed", "total_orders_assigned", "total_money_to_collect", "total_money_collected"], "title": "EmployeeStatsResponseSchema", "type": "object"}, "Company": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"description": "", "title": "Office", "type": "integer"}, "code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Code"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "color_code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "Hex color code for the company", "title": "Color Code"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "name", "created_at", "updated_at"], "title": "Company", "type": "object"}, "EmployeeBalanceTransactionSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "employee": {"$ref": "#/components/schemas/User", "description": "", "title": "Employee"}, "amount": {"anyOf": [{"type": "number"}, {"type": "string"}], "description": "", "title": "Amount"}, "transaction_type": {"$ref": "#/components/schemas/TransactionTypeEnum", "description": "", "title": "Transaction Type"}, "balance_before": {"anyOf": [{"type": "number"}, {"type": "string"}], "description": "", "title": "Balance Before"}, "created_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Created By"}, "order": {"anyOf": [{"$ref": "#/components/schemas/Order"}, {"type": "null"}], "description": "", "title": "Order"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["employee", "amount", "transaction_type", "balance_before", "created_by", "created_at", "updated_at"], "title": "EmployeeBalanceTransactionSchema", "type": "object"}, "Group": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 150, "title": "Name", "type": "string"}, "permissions": {"description": "", "items": {"type": "integer"}, "title": "Permissions", "type": "array"}}, "required": ["name", "permissions"], "title": "Group", "type": "object"}, "Order": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "orders_bulk_sheet": {"anyOf": [{"$ref": "#/components/schemas/OrdersBulkSheet"}, {"type": "null"}], "description": "", "title": "Orders Bulk Sheet"}, "code": {"description": "", "maxLength": 255, "title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount customer should pay", "title": "Total Price"}, "customer_name": {"description": "", "maxLength": 255, "title": "Customer Name", "type": "string"}, "customer_phone": {"description": "List of customer phone numbers separated by `-`", "title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Customer Address"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Special commission rate for the order", "title": "Special Commission Rate"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Delivery Deadline Date"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount paid by the customer", "title": "Delivery Customer Payment"}, "order_handling_status": {"allOf": [{"$ref": "#/components/schemas/OrderHandlingStatusEnum"}], "default": "PENDING", "description": "", "title": "Order Handling Status"}, "order_delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Order Delivery Status"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "null"}], "description": "", "title": "Assigned To"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Assigned At"}, "customer_company": {"anyOf": [{"$ref": "#/components/schemas/Company"}, {"type": "null"}], "description": "", "title": "Customer Company"}, "breakable": {"default": false, "description": "Whether the order contains breakable items like glass", "title": "Breakable", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "code", "customer_name", "customer_phone", "created_at", "updated_at"], "title": "Order", "type": "object"}, "OrderDefaultHandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderDefaultHandlingStatusEnum", "type": "string"}, "OrderDeliveryStatus": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Office"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Description"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDefaultHandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Order Default Handling Status"}, "just_delivery_commission_rate": {"default": false, "description": "", "title": "Just Delivery Commission Rate", "type": "boolean"}, "percentage_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Percentage of the order total price, null if not applicable", "title": "Percentage Commission Rate"}, "percentage_of_order_total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Percentage of the order total price, null if not applicable", "title": "Percentage Of Order Total Price"}}, "required": ["name", "created_at", "updated_at"], "title": "OrderDeliveryStatus", "type": "object"}, "OrderHandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatusEnum", "type": "string"}, "OrdersBulkSheet": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"description": "", "title": "Office", "type": "integer"}, "imported_by": {"description": "User who performed the import", "title": "Imported By", "type": "integer"}, "name": {"description": "Display name for the import", "maxLength": 255, "title": "Name", "type": "string"}, "original_filename": {"description": "Original filename of the uploaded Excel file", "maxLength": 255, "title": "Original Filename", "type": "string"}, "sheet_file": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Stored copy of the imported Excel file", "title": "Sheet File"}, "total_rows_processed": {"default": 0, "description": "Total number of rows processed from Excel", "title": "Total Rows Processed", "type": "integer"}, "successful_imports": {"default": 0, "description": "Number of orders successfully imported", "title": "Successful Imports", "type": "integer"}, "failed_imports": {"default": 0, "description": "Number of rows that failed to import", "title": "Failed Imports", "type": "integer"}, "skipped_rows": {"default": 0, "description": "Number of rows that were skipped", "title": "Skipped Rows", "type": "integer"}, "import_config": {"contentMediaType": "application/json", "contentSchema": {}, "description": "Configuration used for the import (column mapping, etc.)", "title": "Import Config", "type": "string"}, "sheet_id": {"default": "", "description": "", "maxLength": 255, "title": "Sheet Id", "type": "string"}, "sheet_name": {"default": "", "description": "", "maxLength": 255, "title": "Sheet Name", "type": "string"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "imported_by", "name", "original_filename", "created_at", "updated_at"], "title": "OrdersBulkSheet", "type": "object"}, "Permission": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "content_type": {"description": "", "title": "Content Type", "type": "integer"}, "codename": {"description": "", "maxLength": 100, "title": "Codename", "type": "string"}}, "required": ["name", "content_type", "codename"], "title": "Permission", "type": "object"}, "TransactionTypeEnum": {"enum": ["DEPOSIT", "WITHDRAW", "TRANSFER", "COMMISSION", "PAYMENT", "RETURN", "OTHER", "SALARY", "SETTLEMENT"], "title": "TransactionTypeEnum", "type": "string"}, "User": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "password": {"description": "", "maxLength": 128, "title": "Password", "type": "string"}, "last_login": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Login"}, "is_superuser": {"default": false, "description": "Designates that this user has all permissions without explicitly assigning them.", "title": "Superuser Status", "type": "boolean"}, "username": {"description": "Required. 150 characters or fewer. Letters, digits and @/./+/-/_ only.", "maxLength": 150, "title": "Username", "type": "string"}, "first_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "First Name"}, "last_name": {"anyOf": [{"maxLength": 150, "type": "string"}, {"type": "null"}], "description": "", "title": "Last Name"}, "email": {"anyOf": [{"format": "email", "type": "string"}, {"type": "null"}], "description": "", "title": "Email Address"}, "is_staff": {"default": false, "description": "Designates whether the user can log into this admin site.", "title": "Staff Status", "type": "boolean"}, "date_joined": {"description": "", "format": "date-time", "title": "Date Joined", "type": "string"}, "role": {"allOf": [{"$ref": "#/components/schemas/RoleEnum"}], "default": "EMPLOYEE", "description": "", "title": "Role"}, "office": {"anyOf": [{"$ref": "#/components/schemas/Office"}, {"type": "null"}], "description": "", "title": "Office"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Commission rate percentage for delivery employees", "title": "Commission Rate"}, "current_location_lat": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lat"}, "current_location_lng": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "", "title": "Current Location Lng"}, "last_location_update": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Last Location Update"}, "balance": {"default": 0, "description": "", "title": "Balance", "type": "number"}, "groups": {"description": "The groups this user belongs to. A user will get all permissions granted to each of their groups.", "items": {"$ref": "#/components/schemas/Group"}, "title": "Groups", "type": "array"}, "user_permissions": {"description": "Specific permissions for this user.", "items": {"$ref": "#/components/schemas/Permission"}, "title": "User Permissions", "type": "array"}}, "required": ["password", "username", "groups", "user_permissions"], "title": "User", "type": "object"}, "QRCodeGenerateResponseSchema": {"description": "Response schema for QR code generation", "properties": {"qr_token": {"title": "<PERSON>r <PERSON><PERSON>", "type": "string"}, "expires_in": {"default": 300, "description": "Validity period of the QR token in seconds", "title": "Expires In", "type": "integer"}}, "required": ["qr_token"], "title": "QRCodeGenerateResponseSchema", "type": "object"}, "QRCodeGenerateSchema": {"description": "Payload for generating a QR code for a specific employee", "properties": {"employee_id": {"title": "Employee Id", "type": "integer"}}, "required": ["employee_id"], "title": "QRCodeGenerateSchema", "type": "object"}, "QRCodeLoginSchema": {"description": "Input schema for logging-in with a QR code", "properties": {"qr_token": {"title": "<PERSON>r <PERSON><PERSON>", "type": "string"}}, "required": ["qr_token"], "title": "QRCodeLoginSchema", "type": "object"}, "HandlingStatusEnum": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "HandlingStatusEnum", "type": "string"}, "OrderAssignmentHistoryOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "assigned_to": {"$ref": "#/components/schemas/User", "description": "", "title": "Assigned To"}, "assigned_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Assigned By"}, "assigned_at": {"description": "", "format": "date-time", "title": "Assigned At", "type": "string"}}, "required": ["office", "order", "assigned_to", "assigned_by", "assigned_at"], "title": "OrderAssignmentHistoryOutSchema", "type": "object"}, "OrderHandlingStatusHistoryOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "handling_status": {"anyOf": [{"$ref": "#/components/schemas/HandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Handling Status"}, "delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Delivery Status"}, "changed_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Changed By"}, "note": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Note"}, "proof": {"anyOf": [{"$ref": "#/components/schemas/OrderProof"}, {"type": "null"}], "description": "", "title": "Proof"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "order", "changed_by", "created_at", "updated_at"], "title": "OrderHandlingStatusHistoryOutSchema", "type": "object"}, "OrderOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "code": {"description": "", "maxLength": 255, "title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount customer should pay", "title": "Total Price"}, "customer_name": {"description": "", "maxLength": 255, "title": "Customer Name", "type": "string"}, "customer_phone": {"description": "List of customer phone numbers separated by `-`", "title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Customer Address"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Special commission rate for the order", "title": "Special Commission Rate"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Delivery Deadline Date"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount paid by the customer", "title": "Delivery Customer Payment"}, "order_handling_status": {"allOf": [{"$ref": "#/components/schemas/OrderHandlingStatusEnum"}], "default": "PENDING", "description": "", "title": "Order Handling Status"}, "order_delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Order Delivery Status"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "null"}], "description": "", "title": "Assigned To"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Assigned At"}, "customer_company": {"anyOf": [{"$ref": "#/components/schemas/Company"}, {"type": "null"}], "description": "", "title": "Customer Company"}, "breakable": {"default": false, "description": "Whether the order contains breakable items like glass", "title": "Breakable", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "code", "customer_name", "customer_phone", "created_at", "updated_at"], "title": "OrderOutSchema", "type": "object"}, "OrderOutSchemaWithHistory": {"properties": {"order": {"$ref": "#/components/schemas/OrderOutSchema"}, "assigning_history": {"items": {"$ref": "#/components/schemas/OrderAssignmentHistoryOutSchema"}, "title": "Assigning History", "type": "array"}, "handling_status_history": {"items": {"$ref": "#/components/schemas/OrderHandlingStatusHistoryOutSchema"}, "title": "Handling Status History", "type": "array"}, "proofs": {"items": {"$ref": "#/components/schemas/OrderProofOutSchema"}, "title": "Proofs", "type": "array"}}, "required": ["order", "assigning_history", "handling_status_history", "proofs"], "title": "OrderOutSchemaWithHistory", "type": "object"}, "OrderProof": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "proof_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Proof By"}, "proof_type": {"$ref": "#/components/schemas/ProofTypeEnum", "description": "", "title": "Proof Type"}, "proof_img": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Proof Img"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Longitude"}}, "required": ["order", "proof_by", "proof_type", "created_at", "updated_at"], "title": "OrderProof", "type": "object"}, "OrderProofOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "order": {"$ref": "#/components/schemas/Order", "description": ""}, "proof_by": {"$ref": "#/components/schemas/User", "description": "", "title": "Proof By"}, "proof_type": {"$ref": "#/components/schemas/ProofTypeEnum", "description": "", "title": "Proof Type"}, "proof_img": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Proof Img"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "description": "", "title": "Longitude"}}, "required": ["order", "proof_by", "proof_type", "created_at", "updated_at"], "title": "OrderProofOutSchema", "type": "object"}, "ProofTypeEnum": {"enum": ["PROOF_OF_ASSIGNMENT", "PROOF_OF_DELIVERY", "PROOF_OF_RETURN"], "title": "ProofTypeEnum", "type": "string"}, "OrderInSchema": {"properties": {"code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Name"}, "customer_phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Phone"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Address"}, "customer_company": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Customer Company"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Delivery Deadline Date"}, "order_delivery_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Delivery Status"}, "breakable": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Breakable"}, "order_handling_status": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Handling Status"}}, "title": "OrderInSchema", "type": "object"}, "OrderHandlingStatus": {"enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderHandlingStatus", "type": "string"}, "OrderExtendedOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "orders_bulk_sheet": {"anyOf": [{"$ref": "#/components/schemas/OrdersBulkSheet"}, {"type": "null"}], "description": "", "title": "Orders Bulk Sheet"}, "code": {"description": "", "maxLength": 255, "title": "Code", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount customer should pay", "title": "Total Price"}, "customer_name": {"description": "", "maxLength": 255, "title": "Customer Name", "type": "string"}, "customer_phone": {"description": "List of customer phone numbers separated by `-`", "title": "Customer Phone", "type": "string"}, "customer_address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Customer Address"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Special commission rate for the order", "title": "Special Commission Rate"}, "delivery_deadline_date": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Delivery Deadline Date"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Amount paid by the customer", "title": "Delivery Customer Payment"}, "order_handling_status": {"allOf": [{"$ref": "#/components/schemas/OrderHandlingStatusEnum"}], "default": "PENDING", "description": "", "title": "Order Handling Status"}, "order_delivery_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDeliveryStatus"}, {"type": "null"}], "description": "", "title": "Order Delivery Status"}, "assigned_to": {"anyOf": [{"$ref": "#/components/schemas/User"}, {"type": "null"}], "description": "", "title": "Assigned To"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "", "title": "Assigned At"}, "customer_company": {"anyOf": [{"$ref": "#/components/schemas/Company"}, {"type": "null"}], "description": "", "title": "Customer Company"}, "breakable": {"default": false, "description": "Whether the order contains breakable items like glass", "title": "Breakable", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "code", "customer_name", "customer_phone", "created_at", "updated_at"], "title": "OrderExtendedOutSchema", "type": "object"}, "OrderAssignSchema": {"properties": {"employee_id": {"title": "Employee Id", "type": "integer"}, "special_commission_rate": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Special Commission Rate"}}, "required": ["employee_id"], "title": "OrderAssignSchema", "type": "object"}, "OrderTransferSchema": {"properties": {"to_employee_id": {"title": "To Employee Id", "type": "integer"}, "proof_type": {"title": "Proof Type", "type": "string"}, "proof_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Proof Url"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}}, "required": ["to_employee_id", "proof_type"], "title": "OrderTransferSchema", "type": "object"}, "OrderProofInSchema": {"properties": {"proof_type": {"title": "Proof Type", "type": "string"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}}, "required": ["proof_type"], "title": "OrderProofInSchema", "type": "object"}, "OrderCompleteSchema": {"properties": {"proof_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Proof Url"}, "latitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Latitude"}, "longitude": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Longitude"}, "delivery_customer_payment": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Delivery Customer Payment"}, "order_delivery_status": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Delivery Status"}}, "title": "OrderCompleteSchema", "type": "object"}, "OrderStatusOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Office"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "description": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Description"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}, "order_default_handling_status": {"anyOf": [{"$ref": "#/components/schemas/OrderDefaultHandlingStatusEnum"}, {"type": "null"}], "description": "", "title": "Order Default Handling Status"}, "just_delivery_commission_rate": {"default": false, "description": "", "title": "Just Delivery Commission Rate", "type": "boolean"}, "percentage_commission_rate": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Percentage of the order total price, null if not applicable", "title": "Percentage Commission Rate"}, "percentage_of_order_total_price": {"anyOf": [{"type": "number"}, {"type": "string"}, {"type": "null"}], "description": "Percentage of the order total price, null if not applicable", "title": "Percentage Of Order Total Price"}}, "required": ["name", "created_at", "updated_at"], "title": "OrderStatusOutSchema", "type": "object"}, "BulkExportRequest": {"description": "Request schema for bulk order export with filters.", "properties": {"export_type": {"allOf": [{"$ref": "#/components/schemas/ExportType"}], "default": "bulk", "description": "Export type"}, "filters": {"allOf": [{"$ref": "#/components/schemas/OrderExportFilters"}], "description": "Export filters"}, "filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Custom filename for export", "title": "Filename"}, "limit": {"anyOf": [{"exclusiveMinimum": 0, "maximum": 50000, "type": "integer"}, {"type": "null"}], "description": "Maximum number of orders to export", "title": "Limit"}, "export_date": {"anyOf": [{"format": "date", "type": "string"}, {"type": "null"}], "description": "Export date", "title": "Export Date"}}, "title": "BulkExportRequest", "type": "object"}, "DateRangeFilter": {"description": "Date range filter for orders.", "properties": {"start_date": {"anyOf": [{"format": "date", "type": "string"}, {"type": "null"}], "description": "Start date (inclusive)", "title": "Start Date"}, "end_date": {"anyOf": [{"format": "date", "type": "string"}, {"type": "null"}], "description": "End date (inclusive)", "title": "End Date"}}, "title": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "object"}, "ExportType": {"description": "Export type enumeration.", "enum": ["selective", "bulk"], "title": "ExportType", "type": "string"}, "OrderExportFilters": {"description": "Filters for bulk order export.", "properties": {"created_date_range": {"anyOf": [{"$ref": "#/components/schemas/DateRangeFilter"}, {"type": "null"}], "description": "Filter by creation date range"}, "updated_date_range": {"anyOf": [{"$ref": "#/components/schemas/DateRangeFilter"}, {"type": "null"}], "description": "Filter by update date range"}, "status": {"anyOf": [{"items": {"$ref": "#/components/schemas/OrderStatus"}, "type": "array"}, {"type": "null"}], "description": "Filter by order status", "title": "Status"}, "assigned_to_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "description": "Filter by assigned user IDs", "title": "Assigned To Ids"}, "company_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "description": "Filter by company IDs", "title": "Company Ids"}, "company_codes": {"anyOf": [{"items": {"type": "string"}, "type": "array"}, {"type": "null"}], "description": "Filter by company codes", "title": "Company Codes"}, "customer_name_contains": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer name (partial match)", "title": "Customer Name Contains"}, "customer_phone_contains": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Filter by customer phone (partial match)", "title": "Customer Phone Contains"}, "min_total_price": {"anyOf": [{"minimum": 0, "type": "number"}, {"type": "null"}], "description": "Minimum total price filter", "title": "Min Total Price"}, "max_total_price": {"anyOf": [{"minimum": 0, "type": "number"}, {"type": "null"}], "description": "Maximum total price filter", "title": "Max Total Price"}, "office_ids": {"anyOf": [{"items": {"type": "integer"}, "type": "array"}, {"type": "null"}], "description": "Filter by office IDs", "title": "Office Ids"}, "has_notes": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter orders that have notes", "title": "Has Notes"}}, "title": "OrderExportFilters", "type": "object"}, "OrderStatus": {"description": "Order status enumeration for filtering.", "enum": ["PENDING", "ASSIGNED", "PROCESSING", "COMPLETED"], "title": "OrderStatus", "type": "string"}, "SelectiveExportRequest": {"description": "Request schema for selective order export.", "properties": {"export_type": {"allOf": [{"$ref": "#/components/schemas/ExportType"}], "default": "selective", "description": "Export type"}, "order_ids": {"description": "List of order IDs to export", "items": {"type": "integer"}, "minItems": 1, "title": "Order Ids", "type": "array"}, "filename": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Custom filename for export", "title": "Filename"}, "export_date": {"anyOf": [{"format": "date", "type": "string"}, {"type": "null"}], "description": "Export date", "title": "Export Date"}}, "required": ["order_ids"], "title": "SelectiveExportRequest", "type": "object"}, "CompanyOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Code"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "color_code": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "Hex color code for the company", "title": "Color Code"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "name", "created_at", "updated_at"], "title": "CompanyOutSchema", "type": "object"}, "CompanyInSchema": {"properties": {"code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "name": {"minLength": 3, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"minLength": 10, "title": "Phone", "type": "string"}, "color_code": {"minLength": 7, "pattern": "^#[0-9A-Fa-f]{6}$", "title": "Color Code", "type": "string"}}, "required": ["name", "phone", "color_code"], "title": "CompanyInSchema", "type": "object"}, "CompanyEditSchema": {"properties": {"code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Code"}, "name": {"anyOf": [{"minLength": 3, "type": "string"}, {"type": "null"}], "title": "Name"}, "address": {"anyOf": [{"minLength": 10, "type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"anyOf": [{"minLength": 10, "type": "string"}, {"type": "null"}], "title": "Phone"}, "color_code": {"anyOf": [{"minLength": 7, "pattern": "^#[0-9A-Fa-f]{6}$", "type": "string"}, {"type": "null"}], "title": "Color Code"}}, "title": "CompanyEditSchema", "type": "object"}, "CompanyChannelOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "office": {"$ref": "#/components/schemas/Office", "description": ""}, "company": {"anyOf": [{"$ref": "#/components/schemas/Company"}, {"type": "null"}], "description": "", "title": "Company"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Notes"}, "channel_whatsapp_number": {"description": "WhatsApp number for the channel", "maxLength": 255, "title": "Channel Whatsapp Number", "type": "string"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["office", "name", "channel_whatsapp_number", "created_at", "updated_at"], "title": "CompanyChannelOutSchema", "type": "object"}, "CompanyChannelInSchema": {"properties": {"company_id": {"description": "ID of the company this channel belongs to", "title": "Company Id", "type": "integer"}, "name": {"description": "Name of the channel", "minLength": 3, "title": "Name", "type": "string"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Additional notes about the channel", "title": "Notes"}, "channel_whatsapp_number": {"description": "WhatsApp number for the channel", "minLength": 10, "title": "Channel Whatsapp Number", "type": "string"}}, "required": ["company_id", "name", "channel_whatsapp_number"], "title": "CompanyChannelInSchema", "type": "object"}, "CompanyChannelEditSchema": {"properties": {"company_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "ID of the company this channel belongs to", "title": "Company Id"}, "name": {"anyOf": [{"minLength": 3, "type": "string"}, {"type": "null"}], "description": "Name of the channel", "title": "Name"}, "notes": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Additional notes about the channel", "title": "Notes"}, "channel_whatsapp_number": {"anyOf": [{"minLength": 10, "type": "string"}, {"type": "null"}], "description": "WhatsApp number for the channel", "title": "Channel Whatsapp Number"}}, "title": "CompanyChannelEditSchema", "type": "object"}, "OfficeOutSchema": {"properties": {"id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "", "title": "Id"}, "name": {"description": "", "maxLength": 255, "title": "Name", "type": "string"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "", "title": "Address"}, "phone": {"anyOf": [{"maxLength": 20, "type": "string"}, {"type": "null"}], "description": "", "title": "Phone"}, "is_active": {"default": true, "description": "", "title": "Is Active", "type": "boolean"}, "created_at": {"description": "", "format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"description": "", "format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["name", "created_at", "updated_at"], "title": "OfficeOutSchema", "type": "object"}, "OfficeEditSchema": {"properties": {"name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Name"}, "address": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Address"}, "phone": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Phone"}, "is_active": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "title": "Is Active"}}, "title": "OfficeEditSchema", "type": "object"}, "OfficePerformanceSchema": {"properties": {"total_orders": {"title": "Total Orders", "type": "integer"}, "orders_by_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Status", "type": "object"}, "orders_by_delivery_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Delivery Status", "type": "object"}, "total_revenue": {"title": "Total Revenue", "type": "number"}, "average_order_value": {"title": "Average Order Value", "type": "number"}, "collection_rate": {"title": "Collection Rate", "type": "number"}, "average_completion_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Completion Time"}, "on_time_delivery_rate": {"title": "On Time Delivery Rate", "type": "number"}, "orders_by_company": {"items": {"additionalProperties": true, "type": "object"}, "title": "Orders By Company", "type": "array"}, "top_performing_employees": {"items": {"additionalProperties": true, "type": "object"}, "title": "Top Performing Employees", "type": "array"}, "daily_performance": {"items": {"additionalProperties": true, "type": "object"}, "title": "Daily Performance", "type": "array"}}, "required": ["total_orders", "orders_by_status", "orders_by_delivery_status", "total_revenue", "average_order_value", "collection_rate", "on_time_delivery_rate", "orders_by_company", "top_performing_employees", "daily_performance"], "title": "OfficePerformanceSchema", "type": "object"}, "DateRangeSchema": {"properties": {"start_date": {"format": "date", "title": "Start Date", "type": "string"}, "end_date": {"format": "date", "title": "End Date", "type": "string"}}, "required": ["start_date", "end_date"], "title": "DateRangeSchema", "type": "object"}, "DailyPerformanceSchema": {"properties": {"date": {"format": "date", "title": "Date", "type": "string"}, "total_orders": {"title": "Total Orders", "type": "integer"}, "completed_orders": {"title": "Completed Orders", "type": "integer"}, "revenue": {"title": "Revenue", "type": "number"}}, "required": ["date", "total_orders", "completed_orders", "revenue"], "title": "DailyPerformanceSchema", "type": "object"}, "EmployeePerformanceSchema": {"properties": {"employee_id": {"title": "Employee Id", "type": "integer"}, "employee_name": {"title": "Employee Name", "type": "string"}, "total_orders_assigned": {"title": "Total Orders Assigned", "type": "integer"}, "total_orders_completed": {"title": "Total Orders Completed", "type": "integer"}, "completion_rate": {"title": "Completion Rate", "type": "number"}, "average_completion_time": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Average Completion Time"}, "total_revenue": {"title": "Total Revenue", "type": "number"}, "commission_earned": {"title": "Commission Earned", "type": "number"}, "average_order_value": {"title": "Average Order Value", "type": "number"}, "on_time_delivery_percentage": {"title": "On Time Delivery Percentage", "type": "number"}, "orders_by_status": {"additionalProperties": {"type": "integer"}, "title": "Orders By Status", "type": "object"}, "daily_performance": {"items": {"$ref": "#/components/schemas/DailyPerformanceSchema"}, "title": "Daily Performance", "type": "array"}}, "required": ["employee_id", "employee_name", "total_orders_assigned", "total_orders_completed", "completion_rate", "total_revenue", "commission_earned", "average_order_value", "on_time_delivery_percentage", "orders_by_status", "daily_performance"], "title": "EmployeePerformanceSchema", "type": "object"}, "HistoricalActivityResponseSchema": {"properties": {"activities": {"items": {"$ref": "#/components/schemas/HistoricalActivitySchema"}, "title": "Activities", "type": "array"}, "total_count": {"title": "Total Count", "type": "integer"}, "page": {"title": "Page", "type": "integer"}, "page_size": {"title": "<PERSON>", "type": "integer"}, "has_more": {"title": "Has <PERSON>", "type": "boolean"}}, "required": ["activities", "total_count", "page", "page_size", "has_more"], "title": "HistoricalActivityResponseSchema", "type": "object"}, "HistoricalActivitySchema": {"properties": {"id": {"title": "Id", "type": "string"}, "timestamp": {"format": "date-time", "title": "Timestamp", "type": "string"}, "activity_type": {"title": "Activity Type", "type": "string"}, "user_name": {"title": "User Name", "type": "string"}, "user_id": {"title": "User Id", "type": "integer"}, "description": {"title": "Description", "type": "string"}, "order_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "title": "Order Id"}, "order_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Code"}, "customer_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Name"}, "details": {"anyOf": [{"additionalProperties": true, "type": "object"}, {"type": "null"}], "title": "Details"}}, "required": ["id", "timestamp", "activity_type", "user_name", "user_id", "description"], "title": "HistoricalActivitySchema", "type": "object"}, "BulkImportListRequest": {"description": "Request schema for listing bulk imports with filters.", "properties": {"page": {"default": 1, "description": "Page number", "minimum": 1, "title": "Page", "type": "integer"}, "page_size": {"default": 20, "description": "Items per page", "maximum": 100, "minimum": 1, "title": "<PERSON>", "type": "integer"}, "search": {"anyOf": [{"type": "string"}, {"type": "null"}], "description": "Search in name or filename", "title": "Search"}, "imported_by_id": {"anyOf": [{"type": "integer"}, {"type": "null"}], "description": "Filter by user who imported", "title": "Imported By Id"}, "date_from": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "Filter imports from this date", "title": "Date From"}, "date_to": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "description": "Filter imports to this date", "title": "Date To"}, "min_success_rate": {"anyOf": [{"maximum": 100, "minimum": 0, "type": "number"}, {"type": "null"}], "description": "Minimum success rate", "title": "Min Success Rate"}, "has_orders": {"anyOf": [{"type": "boolean"}, {"type": "null"}], "description": "Filter by whether bulk import has orders", "title": "Has Orders"}}, "title": "BulkImportListRequest", "type": "object"}, "BulkImportListResponse": {"description": "Response schema for listing bulk imports.", "properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "total_count": {"title": "Total Count", "type": "integer"}, "bulk_imports": {"items": {"$ref": "#/components/schemas/BulkImportSummarySchema"}, "title": "Bulk Imports", "type": "array"}, "page": {"default": 1, "title": "Page", "type": "integer"}, "page_size": {"default": 20, "title": "<PERSON>", "type": "integer"}, "total_pages": {"title": "Total Pages", "type": "integer"}}, "required": ["total_count", "bulk_imports", "total_pages"], "title": "BulkImportListResponse", "type": "object"}, "BulkImportSummarySchema": {"description": "Summary information for a bulk import session.", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "original_filename": {"title": "Original Filename", "type": "string"}, "imported_by_username": {"title": "Imported By Username", "type": "string"}, "imported_by_full_name": {"title": "Imported By Full Name", "type": "string"}, "total_rows_processed": {"title": "Total Rows Processed", "type": "integer"}, "successful_imports": {"title": "Successful Imports", "type": "integer"}, "failed_imports": {"title": "Failed Imports", "type": "integer"}, "skipped_rows": {"title": "Skipped Rows", "type": "integer"}, "success_rate": {"title": "Success Rate", "type": "number"}, "orders_count": {"title": "Orders Count", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "name", "original_filename", "imported_by_username", "imported_by_full_name", "total_rows_processed", "successful_imports", "failed_imports", "skipped_rows", "success_rate", "orders_count", "created_at", "updated_at"], "title": "BulkImportSummarySchema", "type": "object"}, "BulkImportDetailResponse": {"description": "Response schema for bulk import details.", "properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "bulk_import": {"$ref": "#/components/schemas/BulkImportWithOrdersSchema"}}, "required": ["bulk_import"], "title": "BulkImportDetailResponse", "type": "object"}, "BulkImportWithOrdersSchema": {"description": "Bulk import with associated orders list.", "properties": {"id": {"title": "Id", "type": "integer"}, "name": {"title": "Name", "type": "string"}, "original_filename": {"title": "Original Filename", "type": "string"}, "imported_by_username": {"title": "Imported By Username", "type": "string"}, "imported_by_full_name": {"title": "Imported By Full Name", "type": "string"}, "total_rows_processed": {"title": "Total Rows Processed", "type": "integer"}, "successful_imports": {"title": "Successful Imports", "type": "integer"}, "failed_imports": {"title": "Failed Imports", "type": "integer"}, "skipped_rows": {"title": "Skipped Rows", "type": "integer"}, "success_rate": {"title": "Success Rate", "type": "number"}, "orders_count": {"title": "Orders Count", "type": "integer"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}, "sheet_file_url": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Sheet File Url"}, "import_config": {"additionalProperties": true, "title": "Import Config", "type": "object"}, "sheet_id": {"default": "", "title": "Sheet Id", "type": "string"}, "sheet_name": {"default": "", "title": "Sheet Name", "type": "string"}, "orders": {"items": {"$ref": "#/components/schemas/OrderSummarySchema"}, "title": "Orders", "type": "array"}}, "required": ["id", "name", "original_filename", "imported_by_username", "imported_by_full_name", "total_rows_processed", "successful_imports", "failed_imports", "skipped_rows", "success_rate", "orders_count", "created_at", "updated_at"], "title": "BulkImportWithOrdersSchema", "type": "object"}, "OrderSummarySchema": {"description": "Summary information for an order within a bulk import.", "properties": {"id": {"title": "Id", "type": "integer"}, "code": {"title": "Code", "type": "string"}, "customer_name": {"title": "Customer Name", "type": "string"}, "customer_phone": {"title": "Customer Phone", "type": "string"}, "customer_address": {"title": "Customer Address", "type": "string"}, "total_price": {"anyOf": [{"type": "number"}, {"type": "null"}], "title": "Total Price"}, "customer_company_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Company Name"}, "customer_company_code": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Customer Company Code"}, "order_handling_status": {"title": "Order Handling Status", "type": "string"}, "order_delivery_status_name": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Order Delivery Status Name"}, "assigned_to_username": {"anyOf": [{"type": "string"}, {"type": "null"}], "title": "Assigned To Username"}, "assigned_at": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Assigned At"}, "created_at": {"format": "date-time", "title": "Created At", "type": "string"}, "updated_at": {"format": "date-time", "title": "Updated At", "type": "string"}}, "required": ["id", "code", "customer_name", "customer_phone", "customer_address", "order_handling_status", "created_at", "updated_at"], "title": "OrderSummarySchema", "type": "object"}, "BulkImportDeleteResponse": {"description": "Response schema for bulk import deletion.", "properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "message": {"title": "Message", "type": "string"}, "deleted_bulk_import_id": {"title": "Deleted Bulk Import Id", "type": "integer"}, "deleted_orders_count": {"title": "Deleted Orders Count", "type": "integer"}}, "required": ["message", "deleted_bulk_import_id", "deleted_orders_count"], "title": "BulkImportDeleteResponse", "type": "object"}, "BulkImportStatsResponse": {"description": "Response schema for bulk import statistics.", "properties": {"success": {"default": true, "title": "Success", "type": "boolean"}, "stats": {"$ref": "#/components/schemas/BulkImportStatsSchema"}}, "required": ["stats"], "title": "BulkImportStatsResponse", "type": "object"}, "BulkImportStatsSchema": {"description": "Statistics for bulk imports.", "properties": {"total_bulk_imports": {"title": "Total Bulk Imports", "type": "integer"}, "total_orders_imported": {"title": "Total Orders Imported", "type": "integer"}, "total_successful_imports": {"title": "Total Successful Imports", "type": "integer"}, "total_failed_imports": {"title": "Total Failed Imports", "type": "integer"}, "average_success_rate": {"title": "Average Success Rate", "type": "number"}, "most_recent_import": {"anyOf": [{"format": "date-time", "type": "string"}, {"type": "null"}], "title": "Most Recent Import"}}, "required": ["total_bulk_imports", "total_orders_imported", "total_successful_imports", "total_failed_imports", "average_success_rate"], "title": "BulkImportStatsSchema", "type": "object"}}, "securitySchemes": {"AuthBearer": {"type": "http", "scheme": "bearer"}}}, "servers": []}