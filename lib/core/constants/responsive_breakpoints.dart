import 'package:flutter/material.dart';

/// Responsive breakpoints for different screen sizes
class ResponsiveBreakpoints {
  // Breakpoint values (in logical pixels)
  static const double mobile = 480;
  static const double tablet = 768;
  static const double desktop = 1024;
  static const double largeDesktop = 1440;

  // Maximum content widths for different screen sizes
  static const double maxMobileWidth = 480;
  static const double maxTabletWidth = 768;
  static const double maxDesktopWidth = 1200;
  static const double maxLargeDesktopWidth = 1440;

  // Form container widths for different screen sizes
  static const double mobileFormWidth = double.infinity;
  static const double tabletFormWidth = 500;
  static const double desktopFormWidth = 450;

  // Grid column counts for different screen sizes
  static const int mobileColumns = 1;
  static const int tabletColumns = 2;
  static const int desktopColumns = 3;
  static const int largeDesktopColumns = 4;

  // Padding values for different screen sizes
  static const EdgeInsets mobilePadding = EdgeInsets.all(16);
  static const EdgeInsets tabletPadding = EdgeInsets.all(24);
  static const EdgeInsets desktopPadding = EdgeInsets.all(32);

  // Content padding for forms and cards
  static const EdgeInsets mobileContentPadding = EdgeInsets.all(20);
  static const EdgeInsets tabletContentPadding = EdgeInsets.all(24);
  static const EdgeInsets desktopContentPadding = EdgeInsets.all(32);
}

/// Device type enumeration
enum DeviceType {
  mobile,
  tablet,
  desktop,
  largeDesktop,
}

/// Responsive utilities for determining device type and screen properties
class ResponsiveUtils {
  /// Get the current device type based on screen width
  static DeviceType getDeviceType(BuildContext context) {
    final width = MediaQuery.of(context).size.width;
    
    if (width < ResponsiveBreakpoints.mobile) {
      return DeviceType.mobile;
    } else if (width < ResponsiveBreakpoints.tablet) {
      return DeviceType.mobile;
    } else if (width < ResponsiveBreakpoints.desktop) {
      return DeviceType.tablet;
    } else if (width < ResponsiveBreakpoints.largeDesktop) {
      return DeviceType.desktop;
    } else {
      return DeviceType.largeDesktop;
    }
  }

  /// Check if the current device is mobile
  static bool isMobile(BuildContext context) {
    return getDeviceType(context) == DeviceType.mobile;
  }

  /// Check if the current device is tablet
  static bool isTablet(BuildContext context) {
    return getDeviceType(context) == DeviceType.tablet;
  }

  /// Check if the current device is desktop
  static bool isDesktop(BuildContext context) {
    final deviceType = getDeviceType(context);
    return deviceType == DeviceType.desktop || deviceType == DeviceType.largeDesktop;
  }

  /// Check if the current device is large desktop
  static bool isLargeDesktop(BuildContext context) {
    return getDeviceType(context) == DeviceType.largeDesktop;
  }

  /// Get appropriate padding based on device type
  static EdgeInsets getPadding(BuildContext context) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return ResponsiveBreakpoints.mobilePadding;
      case DeviceType.tablet:
        return ResponsiveBreakpoints.tabletPadding;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return ResponsiveBreakpoints.desktopPadding;
    }
  }

  /// Get appropriate content padding based on device type
  static EdgeInsets getContentPadding(BuildContext context) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return ResponsiveBreakpoints.mobileContentPadding;
      case DeviceType.tablet:
        return ResponsiveBreakpoints.tabletContentPadding;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return ResponsiveBreakpoints.desktopContentPadding;
    }
  }

  /// Get appropriate form width based on device type
  static double getFormWidth(BuildContext context) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return ResponsiveBreakpoints.mobileFormWidth;
      case DeviceType.tablet:
        return ResponsiveBreakpoints.tabletFormWidth;
      case DeviceType.desktop:
      case DeviceType.largeDesktop:
        return ResponsiveBreakpoints.desktopFormWidth;
    }
  }

  /// Get appropriate grid column count based on device type
  static int getGridColumns(BuildContext context) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return ResponsiveBreakpoints.mobileColumns;
      case DeviceType.tablet:
        return ResponsiveBreakpoints.tabletColumns;
      case DeviceType.desktop:
        return ResponsiveBreakpoints.desktopColumns;
      case DeviceType.largeDesktop:
        return ResponsiveBreakpoints.largeDesktopColumns;
    }
  }

  /// Get maximum content width based on device type
  static double getMaxContentWidth(BuildContext context) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return ResponsiveBreakpoints.maxMobileWidth;
      case DeviceType.tablet:
        return ResponsiveBreakpoints.maxTabletWidth;
      case DeviceType.desktop:
        return ResponsiveBreakpoints.maxDesktopWidth;
      case DeviceType.largeDesktop:
        return ResponsiveBreakpoints.maxLargeDesktopWidth;
    }
  }

  /// Get responsive value based on device type
  static T getResponsiveValue<T>(
    BuildContext context, {
    required T mobile,
    T? tablet,
    T? desktop,
    T? largeDesktop,
  }) {
    switch (getDeviceType(context)) {
      case DeviceType.mobile:
        return mobile;
      case DeviceType.tablet:
        return tablet ?? mobile;
      case DeviceType.desktop:
        return desktop ?? tablet ?? mobile;
      case DeviceType.largeDesktop:
        return largeDesktop ?? desktop ?? tablet ?? mobile;
    }
  }

  /// Get screen width
  static double getScreenWidth(BuildContext context) {
    return MediaQuery.of(context).size.width;
  }

  /// Get screen height
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// Check if screen is in landscape mode
  static bool isLandscape(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.landscape;
  }

  /// Check if screen is in portrait mode
  static bool isPortrait(BuildContext context) {
    return MediaQuery.of(context).orientation == Orientation.portrait;
  }
}
