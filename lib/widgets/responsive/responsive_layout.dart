import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/responsive_breakpoints.dart';

/// A responsive layout wrapper that adapts content based on screen size
class ResponsiveLayout extends StatelessWidget {
  final Widget child;
  final bool centerContent;
  final bool constrainWidth;
  final double? maxWidth;
  final EdgeInsets? padding;
  final Color? backgroundColor;

  const ResponsiveLayout({
    super.key,
    required this.child,
    this.centerContent = true,
    this.constrainWidth = true,
    this.maxWidth,
    this.padding,
    this.backgroundColor,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? ResponsiveUtils.getPadding(context);
    final maxContentWidth = maxWidth ?? ResponsiveUtils.getMaxContentWidth(context);

    Widget content = child;

    // Apply width constraints if needed
    if (constrainWidth && !ResponsiveUtils.isMobile(context)) {
      content = ConstrainedBox(
        constraints: BoxConstraints(maxWidth: maxContentWidth),
        child: content,
      );
    }

    // Center content if needed
    if (centerContent && !ResponsiveUtils.isMobile(context)) {
      content = Center(child: content);
    }

    // Apply padding
    content = Padding(
      padding: responsivePadding,
      child: content,
    );

    // Apply background color if provided
    if (backgroundColor != null) {
      content = Container(
        color: backgroundColor,
        child: content,
      );
    }

    return content;
  }
}

/// A responsive container for forms and cards
class ResponsiveContainer extends StatelessWidget {
  final Widget child;
  final EdgeInsets? padding;
  final EdgeInsets? margin;
  final Decoration? decoration;
  final double? width;
  final double? height;
  final bool centerContent;

  const ResponsiveContainer({
    super.key,
    required this.child,
    this.padding,
    this.margin,
    this.decoration,
    this.width,
    this.height,
    this.centerContent = false,
  });

  @override
  Widget build(BuildContext context) {
    final responsivePadding = padding ?? ResponsiveUtils.getContentPadding(context);
    final containerWidth = width ?? ResponsiveUtils.getFormWidth(context);

    Widget content = Container(
      width: containerWidth == double.infinity ? null : containerWidth,
      height: height,
      padding: responsivePadding,
      margin: margin,
      decoration: decoration,
      child: child,
    );

    if (centerContent && !ResponsiveUtils.isMobile(context)) {
      content = Center(child: content);
    }

    return content;
  }
}

/// A responsive grid that adapts column count based on screen size
class ResponsiveGrid extends StatelessWidget {
  final List<Widget> children;
  final double spacing;
  final double runSpacing;
  final double? childAspectRatio;
  final int? mobileColumns;
  final int? tabletColumns;
  final int? desktopColumns;
  final int? largeDesktopColumns;
  final bool shrinkWrap;
  final ScrollPhysics? physics;

  const ResponsiveGrid({
    super.key,
    required this.children,
    this.spacing = 12,
    this.runSpacing = 12,
    this.childAspectRatio,
    this.mobileColumns,
    this.tabletColumns,
    this.desktopColumns,
    this.largeDesktopColumns,
    this.shrinkWrap = true,
    this.physics = const NeverScrollableScrollPhysics(),
  });

  @override
  Widget build(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    
    int columnCount;
    switch (deviceType) {
      case DeviceType.mobile:
        columnCount = mobileColumns ?? ResponsiveBreakpoints.mobileColumns;
        break;
      case DeviceType.tablet:
        columnCount = tabletColumns ?? ResponsiveBreakpoints.tabletColumns;
        break;
      case DeviceType.desktop:
        columnCount = desktopColumns ?? ResponsiveBreakpoints.desktopColumns;
        break;
      case DeviceType.largeDesktop:
        columnCount = largeDesktopColumns ?? ResponsiveBreakpoints.largeDesktopColumns;
        break;
    }

    return GridView.count(
      crossAxisCount: columnCount,
      crossAxisSpacing: spacing,
      mainAxisSpacing: runSpacing,
      childAspectRatio: childAspectRatio ?? 1.0,
      shrinkWrap: shrinkWrap,
      physics: physics,
      children: children,
    );
  }
}

/// A responsive row that can wrap to column on smaller screens
class ResponsiveRow extends StatelessWidget {
  final List<Widget> children;
  final MainAxisAlignment mainAxisAlignment;
  final CrossAxisAlignment crossAxisAlignment;
  final MainAxisSize mainAxisSize;
  final bool wrapOnMobile;
  final double spacing;

  const ResponsiveRow({
    super.key,
    required this.children,
    this.mainAxisAlignment = MainAxisAlignment.start,
    this.crossAxisAlignment = CrossAxisAlignment.center,
    this.mainAxisSize = MainAxisSize.max,
    this.wrapOnMobile = true,
    this.spacing = 8,
  });

  @override
  Widget build(BuildContext context) {
    if (wrapOnMobile && ResponsiveUtils.isMobile(context)) {
      return Column(
        mainAxisAlignment: mainAxisAlignment,
        crossAxisAlignment: crossAxisAlignment,
        mainAxisSize: mainAxisSize,
        children: children
            .expand((child) => [child, SizedBox(height: spacing)])
            .take(children.length * 2 - 1)
            .toList(),
      );
    }

    return Row(
      mainAxisAlignment: mainAxisAlignment,
      crossAxisAlignment: crossAxisAlignment,
      mainAxisSize: mainAxisSize,
      children: children
          .expand((child) => [child, SizedBox(width: spacing)])
          .take(children.length * 2 - 1)
          .toList(),
    );
  }
}

/// A responsive builder that provides different widgets based on screen size
class ResponsiveBuilder extends StatelessWidget {
  final Widget mobile;
  final Widget? tablet;
  final Widget? desktop;
  final Widget? largeDesktop;

  const ResponsiveBuilder({
    super.key,
    required this.mobile,
    this.tablet,
    this.desktop,
    this.largeDesktop,
  });

  @override
  Widget build(BuildContext context) {
    return ResponsiveUtils.getResponsiveValue(
      context,
      mobile: mobile,
      tablet: tablet,
      desktop: desktop,
      largeDesktop: largeDesktop,
    );
  }
}

/// A responsive scaffold that adapts app bar and drawer behavior
class ResponsiveScaffold extends StatelessWidget {
  final Widget? appBar;
  final Widget body;
  final Widget? drawer;
  final Widget? endDrawer;
  final Widget? bottomNavigationBar;
  final Widget? floatingActionButton;
  final Color? backgroundColor;
  final bool extendBodyBehindAppBar;

  const ResponsiveScaffold({
    super.key,
    this.appBar,
    required this.body,
    this.drawer,
    this.endDrawer,
    this.bottomNavigationBar,
    this.floatingActionButton,
    this.backgroundColor,
    this.extendBodyBehindAppBar = false,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: appBar as PreferredSizeWidget?,
      body: ResponsiveLayout(child: body),
      drawer: drawer,
      endDrawer: endDrawer,
      bottomNavigationBar: bottomNavigationBar,
      floatingActionButton: floatingActionButton,
      backgroundColor: backgroundColor,
      extendBodyBehindAppBar: extendBodyBehindAppBar,
    );
  }
}
