import 'package:flutter/material.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/core/constants/responsive_breakpoints.dart';
import 'package:myrunway/widgets/responsive/responsive_layout.dart';

/// Demo page to test responsive design implementation
class ResponsiveDemoPage extends StatelessWidget {
  const ResponsiveDemoPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Responsive Design Demo'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: ResponsiveLayout(
        child: SingleChildScrollView(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _buildDeviceInfoCard(context),
              const SizedBox(height: 24),
              _buildResponsiveGrid(context),
              const SizedBox(height: 24),
              _buildResponsiveContainer(context),
              const SizedBox(height: 24),
              _buildResponsiveRow(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDeviceInfoCard(BuildContext context) {
    final deviceType = ResponsiveUtils.getDeviceType(context);
    final screenWidth = ResponsiveUtils.getScreenWidth(context);
    final screenHeight = ResponsiveUtils.getScreenHeight(context);
    final isLandscape = ResponsiveUtils.isLandscape(context);

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Device Information',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 12),
            Text('Device Type: ${deviceType.name}'),
            Text('Screen Width: ${screenWidth.toStringAsFixed(0)}px'),
            Text('Screen Height: ${screenHeight.toStringAsFixed(0)}px'),
            Text('Orientation: ${isLandscape ? 'Landscape' : 'Portrait'}'),
            Text('Is Mobile: ${ResponsiveUtils.isMobile(context)}'),
            Text('Is Tablet: ${ResponsiveUtils.isTablet(context)}'),
            Text('Is Desktop: ${ResponsiveUtils.isDesktop(context)}'),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveGrid(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Responsive Grid',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 12),
            ResponsiveGrid(
              children: List.generate(
                8,
                (index) => Container(
                  decoration: BoxDecoration(
                    color: AppColors.primary.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: AppColors.primary),
                  ),
                  child: Center(
                    child: Text(
                      '${index + 1}',
                      style: const TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildResponsiveContainer(BuildContext context) {
    return ResponsiveContainer(
      centerContent: true,
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.grey300),
      ),
      child: Column(
        children: [
          Text(
            'Responsive Container',
            style: Theme.of(context).textTheme.headlineSmall,
          ),
          const SizedBox(height: 12),
          Text(
            'This container adapts its width and padding based on screen size.',
            textAlign: TextAlign.center,
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 16),
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              color: AppColors.primary,
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              'Form Width: ${ResponsiveUtils.getFormWidth(context) == double.infinity ? 'Full Width' : '${ResponsiveUtils.getFormWidth(context).toStringAsFixed(0)}px'}',
              style: const TextStyle(color: Colors.white),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildResponsiveRow(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Responsive Row',
              style: Theme.of(context).textTheme.headlineSmall,
            ),
            const SizedBox(height: 12),
            Text(
              'This row wraps to a column on mobile devices:',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            ResponsiveRow(
              children: [
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.success.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.success),
                    ),
                    child: const Text(
                      'Item 1',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: AppColors.success),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.warning.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.warning),
                    ),
                    child: const Text(
                      'Item 2',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: AppColors.warning),
                    ),
                  ),
                ),
                Expanded(
                  child: Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: AppColors.info.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: AppColors.info),
                    ),
                    child: const Text(
                      'Item 3',
                      textAlign: TextAlign.center,
                      style: TextStyle(color: AppColors.info),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
