import 'dart:async';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/core/services/autocomplete_service.dart';

// Enum for different autocomplete types
enum AutocompleteType {
  customerName,
  customerAddress,
  customerPhone,
  orderCode,
  customerData,
}

// Autocomplete text field widget
class AutocompleteTextField extends StatefulWidget {
  final TextEditingController controller;
  final AutocompleteType type;
  final String labelText;
  final String? hintText;
  final Widget? prefixIcon;
  final String? Function(String?)? validator;
  final TextInputAction? textInputAction;
  final TextInputType? keyboardType;
  final int maxLines;
  final bool enabled;
  final Function(AutocompleteSuggestion)? onSuggestionSelected;
  final Function(String)? onChanged;
  final int maxSuggestions;
  final Duration debounceDelay;
  final double similarityThreshold;

  const AutocompleteTextField({
    super.key,
    required this.controller,
    required this.type,
    required this.labelText,
    this.hintText,
    this.prefixIcon,
    this.validator,
    this.textInputAction,
    this.keyboardType,
    this.maxLines = 1,
    this.enabled = true,
    this.onSuggestionSelected,
    this.onChanged,
    this.maxSuggestions = 5,
    this.debounceDelay = const Duration(milliseconds: 300),
    this.similarityThreshold = 0.75,
  });

  @override
  State<AutocompleteTextField> createState() => _AutocompleteTextFieldState();
}

class _AutocompleteTextFieldState extends State<AutocompleteTextField> {
  final AutocompleteService _autocompleteService =
      Get.find<AutocompleteService>();
  final FocusNode _focusNode = FocusNode();
  final LayerLink _layerLink = LayerLink();

  OverlayEntry? _overlayEntry;
  List<AutocompleteSuggestion> _suggestions = [];
  bool _isLoading = false;
  Timer? _debounceTimer;
  bool showSuggestionsOverlay = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChanged);
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _debounceTimer?.cancel();
    _removeOverlay();
    _focusNode.removeListener(_onFocusChanged);
    _focusNode.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  void _onFocusChanged() {
    if (!_focusNode.hasFocus) {
      _hideSuggestions();
    }
  }

  void _onTextChanged() {
    final text = widget.controller.text;
    widget.onChanged?.call(text);

    _debounceTimer?.cancel();
    _debounceTimer = Timer(widget.debounceDelay, () {
      _fetchSuggestions(text);
    });
  }

  Future<void> _fetchSuggestions(String query) async {
    if (query.trim().isEmpty) {
      _hideSuggestions();
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      List<AutocompleteSuggestion> suggestions = [];

      switch (widget.type) {
        case AutocompleteType.customerName:
          suggestions = await _autocompleteService.getCustomerNameSuggestions(
            query,
            limit: widget.maxSuggestions,
          );
          break;
        case AutocompleteType.customerAddress:
          suggestions = await _autocompleteService
              .getCustomerAddressSuggestions(
                query,
                limit: widget.maxSuggestions,
                similarityThreshold: widget.similarityThreshold,
              );
          break;
        case AutocompleteType.customerPhone:
          suggestions = await _autocompleteService.getCustomerPhoneSuggestions(
            query,
            limit: widget.maxSuggestions,
          );
          break;
        case AutocompleteType.orderCode:
          suggestions = await _autocompleteService.getOrderCodeSuggestions(
            query,
            limit: widget.maxSuggestions,
          );
          break;
        case AutocompleteType.customerData:
          suggestions = await _autocompleteService.getCustomerDataSuggestions(
            query,
            limit: widget.maxSuggestions,
          );
          break;
      }

      setState(() {
        _suggestions = suggestions;
        _isLoading = false;
      });

      if (suggestions.isNotEmpty && _focusNode.hasFocus) {
        _showSuggestions();
      } else {
        _hideSuggestions();
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
        _suggestions = [];
      });
      _hideSuggestions();
    }
  }

  void _showSuggestions() {
    if (_overlayEntry != null) return;

    _overlayEntry = OverlayEntry(
      builder:
          (context) => Positioned(
            width: context.width - 32, // Account for padding
            child: CompositedTransformFollower(
              link: _layerLink,
              showWhenUnlinked: false,
              offset: const Offset(0, 60), // Position below the text field
              child: Material(
                elevation: 4,
                borderRadius: BorderRadius.circular(8),
                child: Container(
                  constraints: const BoxConstraints(maxHeight: 200),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.grey.shade300),
                  ),
                  child: _buildSuggestionsList(),
                ),
              ),
            ),
          ),
    );

    Overlay.of(context).insert(_overlayEntry!);
    setState(() {
      showSuggestionsOverlay = true;
    });
  }

  void _hideSuggestions() {
    _removeOverlay();
    setState(() {
      showSuggestionsOverlay = false;
    });
  }

  void _removeOverlay() {
    _overlayEntry?.remove();
    _overlayEntry = null;
  }

  Widget _buildSuggestionsList() {
    if (_isLoading) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Center(
          child: SizedBox(
            height: 20,
            width: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
        ),
      );
    }

    if (_suggestions.isEmpty) {
      return const Padding(
        padding: EdgeInsets.all(16),
        child: Text(
          'لا توجد اقتراحات',
          style: TextStyle(color: Colors.grey),
          textAlign: TextAlign.center,
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      padding: EdgeInsets.zero,
      itemCount: _suggestions.length,
      itemBuilder: (context, index) {
        final suggestion = _suggestions[index];
        return ListTile(
          dense: true,
          title: Text(suggestion.display, style: const TextStyle(fontSize: 14)),
          subtitle: _buildSuggestionSubtitle(suggestion),
          onTap: () => _onSuggestionTap(suggestion),
        );
      },
    );
  }

  Widget? _buildSuggestionSubtitle(AutocompleteSuggestion suggestion) {
    final metadata = suggestion.metadata;
    if (metadata == null) return null;

    switch (widget.type) {
      case AutocompleteType.customerName:
        final phone = metadata['phone'] as String?;
        final address = metadata['address'] as String?;
        if (phone != null || address != null) {
          return Text(
            [
              phone,
              address,
            ].where((e) => e != null && e.isNotEmpty).join(' - '),
            style: const TextStyle(fontSize: 12, color: Colors.grey),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          );
        }
        break;
      case AutocompleteType.customerAddress:
        final customerName = metadata['customerName'] as String?;
        if (customerName != null) {
          return Text(
            customerName,
            style: const TextStyle(fontSize: 12, color: Colors.grey),
          );
        }
        break;
      case AutocompleteType.orderCode:
        final exists = metadata['exists'] as bool?;
        final suggested = metadata['suggested'] as bool?;
        if (exists == true) {
          return const Text(
            'رمز موجود',
            style: TextStyle(fontSize: 12, color: Colors.orange),
          );
        } else if (suggested == true) {
          return const Text(
            'مقترح',
            style: TextStyle(fontSize: 12, color: Colors.green),
          );
        }
        break;
      default:
        break;
    }

    return null;
  }

  void _onSuggestionTap(AutocompleteSuggestion suggestion) {
    widget.controller.text = suggestion.value;
    widget.onSuggestionSelected?.call(suggestion);
    _hideSuggestions();
    _focusNode.unfocus();
  }

  @override
  Widget build(BuildContext context) {
    return CompositedTransformTarget(
      link: _layerLink,
      child: TextFormField(
        controller: widget.controller,
        focusNode: _focusNode,
        decoration: InputDecoration(
          labelText: widget.labelText,
          hintText: widget.hintText,
          border: const OutlineInputBorder(),
          prefixIcon: widget.prefixIcon,
          suffixIcon:
              _isLoading
                  ? const SizedBox(
                    height: 20,
                    width: 20,
                    child: Padding(
                      padding: EdgeInsets.all(12),
                      child: CircularProgressIndicator(strokeWidth: 2),
                    ),
                  )
                  : null,
        ),
        validator: widget.validator,
        textInputAction: widget.textInputAction,
        keyboardType: widget.keyboardType,
        maxLines: widget.maxLines,
        enabled: widget.enabled,
      ),
    );
  }
}
