import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:myrunway/pages/orders/order_create_controller.dart';
import 'package:myrunway/core/models/order_model_new.dart';
import 'package:myrunway/core/constants/app_colors.dart';
import 'package:myrunway/utils/form_validators.dart';
import 'package:myrunway/widgets/autocomplete_text_field.dart';

class OrderCreatePage extends StatefulWidget {
  const OrderCreatePage({super.key});

  @override
  State<OrderCreatePage> createState() => _OrderCreatePageState();
}

class _OrderCreatePageState extends State<OrderCreatePage> {
  final _formKey = GlobalKey<FormState>();
  final _codeController = TextEditingController();
  final _notesController = TextEditingController();
  final _totalPriceController = TextEditingController();
  final _customerNameController = TextEditingController();
  final _customerPhoneController = TextEditingController();
  final _customerAddressController = TextEditingController();

  final OrderCreateController _controller = Get.put(OrderCreateController());

  int? _selectedCompanyId;
  DateTime? _deliveryDeadline;
  bool _isBreakable = false;

  @override
  void dispose() {
    _codeController.dispose();
    _notesController.dispose();
    _totalPriceController.dispose();
    _customerNameController.dispose();
    _customerPhoneController.dispose();
    _customerAddressController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('إضافة طلب جديد'),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: Form(
        key: _formKey,
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Order Code with Autocomplete
              AutocompleteTextField(
                controller: _codeController,
                type: AutocompleteType.orderCode,
                labelText: 'رمز الطلب *',
                prefixIcon: const Icon(Icons.qr_code),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
                onSuggestionSelected: (suggestion) {
                  // Show warning if code already exists
                  final metadata = suggestion.metadata;
                  if (metadata != null && metadata['exists'] == true) {
                    Get.snackbar(
                      'تحذير',
                      'هذا الرمز موجود بالفعل، يرجى اختيار رمز آخر',
                      backgroundColor: Colors.orange,
                      colorText: Colors.white,
                    );
                  }
                },
              ),
              const SizedBox(height: 16),

              // Customer Name with Autocomplete
              AutocompleteTextField(
                controller: _customerNameController,
                type: AutocompleteType.customerName,
                labelText: 'اسم العميل *',
                prefixIcon: const Icon(Icons.person),
                validator: FormValidators.required,
                textInputAction: TextInputAction.next,
                onSuggestionSelected: (suggestion) {
                  // Auto-fill related fields if available
                  final metadata = suggestion.metadata;
                  if (metadata != null) {
                    if (metadata['phone'] != null &&
                        _customerPhoneController.text.isEmpty) {
                      _customerPhoneController.text = metadata['phone'];
                    }
                    if (metadata['address'] != null &&
                        _customerAddressController.text.isEmpty) {
                      _customerAddressController.text = metadata['address'];
                    }
                  }
                },
              ),
              const SizedBox(height: 16),

              // Customer Phone with Autocomplete
              AutocompleteTextField(
                controller: _customerPhoneController,
                type: AutocompleteType.customerPhone,
                labelText: 'رقم هاتف العميل *',
                prefixIcon: const Icon(Icons.phone),
                validator: FormValidators.phone,
                keyboardType: TextInputType.phone,
                textInputAction: TextInputAction.next,
                onSuggestionSelected: (suggestion) {
                  // Auto-fill related fields if available
                  final metadata = suggestion.metadata;
                  if (metadata != null) {
                    if (metadata['customerName'] != null &&
                        _customerNameController.text.isEmpty) {
                      _customerNameController.text = metadata['customerName'];
                    }
                    if (metadata['address'] != null &&
                        _customerAddressController.text.isEmpty) {
                      _customerAddressController.text = metadata['address'];
                    }
                  }
                },
              ),
              const SizedBox(height: 16),

              // Company Selection
              Obx(
                () => DropdownButtonFormField<int>(
                  value: _selectedCompanyId,
                  decoration: const InputDecoration(
                    labelText: 'الشركة *',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.business),
                  ),
                  items:
                      _controller.companies.map((company) {
                        return DropdownMenuItem<int>(
                          value: company.id,
                          child: Text(company.name),
                        );
                      }).toList(),
                  onChanged: (value) {
                    setState(() {
                      _selectedCompanyId = value;
                    });
                  },
                  validator: (value) {
                    if (value == null) {
                      return 'يرجى اختيار الشركة';
                    }
                    return null;
                  },
                ),
              ),
              const SizedBox(height: 16),

              // Customer Address with Autocomplete
              AutocompleteTextField(
                controller: _customerAddressController,
                type: AutocompleteType.customerAddress,
                labelText: 'عنوان العميل',
                prefixIcon: const Icon(Icons.location_on),
                maxLines: 3,
                textInputAction: TextInputAction.next,
                similarityThreshold:
                    0.75, // Use fuzzy matching for Arabic addresses
                onSuggestionSelected: (suggestion) {
                  // Auto-fill related fields if available
                  final metadata = suggestion.metadata;
                  if (metadata != null) {
                    if (metadata['customerName'] != null &&
                        _customerNameController.text.isEmpty) {
                      _customerNameController.text = metadata['customerName'];
                    }
                    if (metadata['phone'] != null &&
                        _customerPhoneController.text.isEmpty) {
                      _customerPhoneController.text = metadata['phone'];
                    }
                  }
                },
              ),
              const SizedBox(height: 16),

              // Total Price
              TextFormField(
                controller: _totalPriceController,
                decoration: const InputDecoration(
                  labelText: 'إجمالي السعر',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.attach_money),
                  suffixText: 'جنيه',
                ),
                validator: (value) {
                  if (value != null && value.isNotEmpty) {
                    return FormValidators.validateAmount(value);
                  }
                  return null;
                },
                keyboardType: TextInputType.number,
                textInputAction: TextInputAction.next,
              ),
              const SizedBox(height: 16),

              // Delivery Deadline
              InkWell(
                onTap: _selectDeliveryDeadline,
                child: InputDecorator(
                  decoration: const InputDecoration(
                    labelText: 'موعد التسليم',
                    border: OutlineInputBorder(),
                    prefixIcon: Icon(Icons.calendar_today),
                  ),
                  child: Text(
                    _deliveryDeadline != null
                        ? '${_deliveryDeadline!.day}/${_deliveryDeadline!.month}/${_deliveryDeadline!.year}'
                        : 'اختر موعد التسليم',
                    style: TextStyle(
                      color:
                          _deliveryDeadline != null
                              ? Colors.black87
                              : Colors.grey[600],
                    ),
                  ),
                ),
              ),
              const SizedBox(height: 16),

              // Breakable checkbox
              CheckboxListTile(
                title: const Text('يحتوي على أشياء قابلة للكسر'),
                subtitle: const Text('مثل الزجاج أو الأشياء الهشة'),
                value: _isBreakable,
                onChanged: (value) {
                  setState(() {
                    _isBreakable = value ?? false;
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
              ),
              const SizedBox(height: 16),

              // Notes
              TextFormField(
                controller: _notesController,
                decoration: const InputDecoration(
                  labelText: 'ملاحظات',
                  border: OutlineInputBorder(),
                  prefixIcon: Icon(Icons.note),
                ),
                maxLines: 3,
                textInputAction: TextInputAction.done,
              ),
              const SizedBox(height: 32),

              // Create Button
              Obx(
                () => ElevatedButton(
                  onPressed: _controller.isCreating ? null : _createOrder,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppColors.primary,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 16),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12),
                    ),
                  ),
                  child:
                      _controller.isCreating
                          ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(
                              strokeWidth: 2,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                Colors.white,
                              ),
                            ),
                          )
                          : const Text(
                            'إنشاء الطلب',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _selectDeliveryDeadline() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );
    if (picked != null) {
      setState(() {
        _deliveryDeadline = picked;
      });
    }
  }

  Future<void> _createOrder() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    final request = OrderCreateRequest(
      code: _codeController.text.trim(),
      customerName: _customerNameController.text.trim(),
      customerPhone: _customerPhoneController.text.trim(),
      customerCompany: _selectedCompanyId!,
      notes:
          _notesController.text.trim().isEmpty
              ? null
              : _notesController.text.trim(),
      totalPrice:
          _totalPriceController.text.trim().isEmpty
              ? null
              : double.parse(_totalPriceController.text.trim()),
      customerAddress:
          _customerAddressController.text.trim().isEmpty
              ? null
              : _customerAddressController.text.trim(),
      deliveryDeadlineDate: _deliveryDeadline,
      breakable: _isBreakable,
    );

    final success = await _controller.createOrder(request);
    if (success) {
      Get.back(closeOverlays: true);
    }
  }
}
