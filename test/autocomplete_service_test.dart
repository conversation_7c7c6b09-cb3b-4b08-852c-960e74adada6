import 'package:flutter_test/flutter_test.dart';
import 'package:myrunway/core/services/autocomplete_service.dart';
import 'package:myrunway/widgets/autocomplete_text_field.dart';

void main() {
  group('AutocompleteService Tests', () {
    group('AutocompleteSuggestion Model', () {
      test('should create suggestion with value and display text', () {
        final suggestion = AutocompleteSuggestion(
          value: 'test_value',
          displayText: 'Test Display',
        );

        expect(suggestion.value, equals('test_value'));
        expect(suggestion.display, equals('Test Display'));
      });

      test('should use value as display when displayText is null', () {
        final suggestion = AutocompleteSuggestion(value: 'test_value');

        expect(suggestion.display, equals('test_value'));
      });

      test('should handle metadata correctly', () {
        final metadata = {'key': 'value', 'number': 42};
        final suggestion = AutocompleteSuggestion(
          value: 'test',
          metadata: metadata,
        );

        expect(suggestion.metadata, equals(metadata));
        expect(suggestion.metadata!['key'], equals('value'));
        expect(suggestion.metadata!['number'], equals(42));
      });

      test('should implement equality correctly', () {
        final suggestion1 = AutocompleteSuggestion(value: 'test');
        final suggestion2 = AutocompleteSuggestion(value: 'test');
        final suggestion3 = AutocompleteSuggestion(value: 'different');

        expect(suggestion1, equals(suggestion2));
        expect(suggestion1, isNot(equals(suggestion3)));
      });

      test('should implement hashCode correctly', () {
        final suggestion1 = AutocompleteSuggestion(value: 'test');
        final suggestion2 = AutocompleteSuggestion(value: 'test');

        expect(suggestion1.hashCode, equals(suggestion2.hashCode));
      });
    });

    group('Autocomplete Types', () {
      test('should define all required autocomplete types', () {
        // Test that all enum values are defined
        expect(AutocompleteType.customerName, isNotNull);
        expect(AutocompleteType.customerAddress, isNotNull);
        expect(AutocompleteType.customerPhone, isNotNull);
        expect(AutocompleteType.orderCode, isNotNull);
        expect(AutocompleteType.customerData, isNotNull);
      });
    });

    group('Cache Management', () {
      test('should provide cache management methods', () {
        final service = AutocompleteService();

        // These should not throw any errors
        expect(() => service.clearCache(), returnsNormally);
        expect(() => service.clearCacheEntry('test_key'), returnsNormally);
      });
    });
  });
}
